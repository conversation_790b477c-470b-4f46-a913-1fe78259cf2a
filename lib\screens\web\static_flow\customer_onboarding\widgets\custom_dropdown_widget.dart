import 'package:flutter/material.dart';
import 'package:nsl/utils/font_manager.dart';

class CustomDropdownWidget extends StatefulWidget {
  CustomDropdownWidget(
      {super.key,
      required this.label,
      required this.list,
      required this.onChanged,
      required this.value,
      this.closeOnTapOutside = true,
      this.customLabelWidget,
      this.isMultiSelect = false});

  final String label;
  final List list;
  final dynamic
      value; // For single select: String?, For multi select: List<String>
  final Function(dynamic) onChanged;
  final bool closeOnTapOutside;
  final Widget? customLabelWidget;
  final bool isMultiSelect;

  @override
  State<CustomDropdownWidget> createState() => _CustomDropdownWidgetState();
}

class _CustomDropdownWidgetState extends State<CustomDropdownWidget> {
  final LayerLink _layerLink = LayerLink();
  final GlobalKey _key = GlobalKey();
  OverlayEntry? _overlayEntry;
  dynamic _currentValue;
  Set<int> _selectedIndices = {}; // Track selected indices internally

  @override
  void initState() {
    super.initState();
    _currentValue = widget.value;
    _updateSelectedIndices();
  }

  @override
  void didUpdateWidget(CustomDropdownWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.value != widget.value) {
      _currentValue = widget.value;
      _updateSelectedIndices();
    }
  }

  void _updateSelectedIndices() {
    _selectedIndices.clear();
    if (widget.isMultiSelect && _currentValue is List<String>) {
      final selectedValues = _currentValue as List<String>;
      final Map<String, int> valueCount = {};

      // Count how many of each value we need to select
      for (String value in selectedValues) {
        valueCount[value] = (valueCount[value] ?? 0) + 1;
      }

      // Find indices for selected values
      final Map<String, int> foundCount = {};
      for (int i = 0; i < widget.list.length; i++) {
        String item = widget.list[i].toString();
        if (valueCount.containsKey(item)) {
          int currentFound = foundCount[item] ?? 0;
          if (currentFound < valueCount[item]!) {
            _selectedIndices.add(i);
            foundCount[item] = currentFound + 1;
          }
        }
      }
    } else if (!widget.isMultiSelect && _currentValue != null) {
      // For single select, find first occurrence
      for (int i = 0; i < widget.list.length; i++) {
        if (widget.list[i].toString() == _currentValue.toString()) {
          _selectedIndices.add(i);
          break;
        }
      }
    }
  }

  @override
  void dispose() {
    _removeOverlay();
    super.dispose();
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  void _showOverlay() {
    final RenderBox renderBox =
        _key.currentContext!.findRenderObject() as RenderBox;
    final Size size = renderBox.size;
    final Offset offset = renderBox.localToGlobal(Offset.zero);

    _overlayEntry = OverlayEntry(
      builder: (context) => Stack(
        children: [
          if (widget.closeOnTapOutside)
            Positioned.fill(
              child: GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () {
                  _removeOverlay();
                },
              ),
            ),
          Positioned(
            left: offset.dx,
            top: offset.dy + size.height,
            width: size.width,
            child: CompositedTransformFollower(
              link: _layerLink,
              offset: Offset(0, size.height + 5),
              child: _buildOverlayOptions(),
            ),
          ),
        ],
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  void _handleSelection(int index) {
    if (widget.isMultiSelect) {
      setState(() {
        if (_selectedIndices.contains(index)) {
          _selectedIndices.remove(index);
        } else {
          _selectedIndices.add(index);
        }

        // Convert back to List<String> for the callback
        final selectedValues =
            _selectedIndices.map((i) => widget.list[i].toString()).toList();
        _currentValue = selectedValues;
      });

      widget.onChanged(_currentValue);

      // Rebuild overlay to show updated checkmarks
      _removeOverlay();
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _showOverlay();
        }
      });
    } else {
      setState(() {
        _selectedIndices.clear();
        _selectedIndices.add(index);
        _currentValue = widget.list[index].toString();
      });

      widget.onChanged(_currentValue);
      _removeOverlay();
    }
  }

  @override
  Widget build(BuildContext context) {
    return _buildDropdown();
  }

  Widget _buildDropdown() {
    return CompositedTransformTarget(
      link: _layerLink,
      child: MouseRegion(
        cursor: SystemMouseCursors.click,
        child: InkWell(
          key: _key,
          onTap: () {
            if (_overlayEntry == null) {
              _showOverlay();
            } else {
              _removeOverlay();
            }
          },
          child: Container(
            height: 32,
            //width: widget.isMultiSelect ? 200 : 150,
            width:double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 8),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(2),
              color: Colors.white,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: widget.customLabelWidget ??
                      Text(
                        _getDisplayText(
                            _currentValue, widget.label, widget.isMultiSelect),
                        style: FontManager.getCustomStyle(
                          fontSize: FontManager.s10,
                          fontWeight: FontWeight.w400,
                          color: _hasValue(_currentValue, widget.isMultiSelect)
                              ? Colors.black
                              : Colors.grey.shade600,
                          fontFamily: FontManager.fontFamilyTiemposText,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                ),
                Icon(Icons.keyboard_arrow_down,
                    color: Colors.grey.shade600, size: 12),
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _getDisplayText(dynamic value, String label, bool isMultiSelect) {
    if (isMultiSelect) {
      final selectedList = value as List<String>? ?? [];
      if (selectedList.isEmpty) {
        return " $label";
      } else {
        return selectedList.join(', ');
      }
    } else {
      return value ?? " $label";
    }
  }

  bool _hasValue(dynamic value, bool isMultiSelect) {
    if (isMultiSelect) {
      final selectedList = value as List<String>? ?? [];
      return selectedList.isNotEmpty;
    } else {
      return value != null;
    }
  }

  Widget _buildOverlayOptions() {
    return Material(
      elevation: 0,
      borderRadius: BorderRadius.circular(4),
      child: Container(
        constraints: BoxConstraints(maxHeight: 200),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(2),
          color: Colors.white,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Flexible(
              child: ListView(
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                children: widget.list.asMap().entries.map((entry) {
                  final index = entry.key;
                  final item = entry.value;
                  final isSelected = _selectedIndices.contains(index);

                  return InkWell(
                    onTap: () {
                      _handleSelection(index);
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 10),
                      decoration: BoxDecoration(
                        color:
                            //  isSelected ? Colors.green.shade50 :
                            Colors.transparent,
                      ),
                      child: Row(
                        children: [
                          if (widget.isMultiSelect)
                            Container(
                              margin: const EdgeInsets.only(right: 10),
                              width: 18,
                              height: 18,
                              decoration: BoxDecoration(
                                border: Border.all(
                                  color: isSelected
                                      ? const Color(0xFF007AFF)
                                      : Colors.grey.shade400,
                                  width: 1.5,
                                ),
                                borderRadius: BorderRadius.circular(3),
                                color: isSelected
                                    ? const Color(0xFF007AFF)
                                    : Colors.transparent,
                              ),
                              child: isSelected
                                  ? Icon(
                                      Icons.check,
                                      size: 14,
                                      color: Colors.white,
                                    )
                                  : null,
                            ),
                          Expanded(
                            child: Text(
                              item.toString(),
                              style: FontManager.getCustomStyle(
                                fontSize: FontManager.s12,
                                color:
                                    // isSelected ? Colors.green.shade700 :
                                    Colors.black,
                                fontWeight: isSelected
                                    ? FontWeight.w500
                                    : FontWeight.w400,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                }).toList(),
              ),
            ),
            // if (widget.isMultiSelect && !widget.closeOnTapOutside)
            //   Container(
            //     width: double.infinity,
            //     decoration: BoxDecoration(
            //       border: Border(top: BorderSide(color: Colors.grey.shade300)),
            //       color: Colors.grey.shade50,
            //     ),
            //     child: TextButton(
            //       onPressed: _removeOverlay,
            //       style: TextButton.styleFrom(
            //         padding: EdgeInsets.symmetric(vertical: 8),
            //         shape: RoundedRectangleBorder(
            //           borderRadius: BorderRadius.only(
            //             bottomLeft: Radius.circular(2),
            //             bottomRight: Radius.circular(2),
            //           ),
            //         ),
            //       ),
            //       child: Text(
            //         'Done',
            //         style: FontManager.getCustomStyle(
            //           fontSize: FontManager.s12,
            //           color: Colors.green.shade700,
            //           fontWeight: FontWeight.w500,
            //         ),
            //       ),
            //     ),
            //   ),
          ],
        ),
      ),
    );
  }
}
