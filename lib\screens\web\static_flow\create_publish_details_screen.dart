import 'package:flutter/material.dart';
import 'package:nsl/l10n/app_localizations.dart';
import 'package:nsl/models/custom_image.dart';
import 'package:nsl/providers/object_creation_provider.dart';
import 'package:nsl/screens/web/new_design/widgets/custom_tab_header.dart';
import 'package:nsl/theme/app_colors.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:provider/provider.dart';

class CreatePublishDetailsScreen extends StatefulWidget {
  const CreatePublishDetailsScreen({super.key});

  @override
  State<CreatePublishDetailsScreen> createState() =>
      _CreatePublishDetailsScreenState();
}

class _CreatePublishDetailsScreenState
    extends State<CreatePublishDetailsScreen> {
  // State variables for tablet layout functionality
  String _selectedTab = '';
  String _selectedPublishTab = '';
  bool _isLoadingApiData = false;

  // Search functionality
  final TextEditingController _searchController = TextEditingController();
  bool _showSearchBar = false;
  List<Map<String, dynamic>> _filteredSampleData = [];
  List<Map<String, dynamic>> _filteredPublishedData = [];

  // Sample data for demonstration (you can replace with actual data)
  final List<Map<String, dynamic>> _sampleData = [
    {
      'fileName': 'Customer Management Solution',
      'type': 'Solution',
      'lastOpened': DateTime.now().subtract(const Duration(days: 1)),
      'publishedDate': DateTime.now().subtract(const Duration(days: 1)),
      'isFavorite': true,
      'status': 'Saved',
    },
    {
      'fileName': 'Inventory Tracking System',
      'type': 'Solution',
      'lastOpened': DateTime.now().subtract(const Duration(days: 2)),
      'publishedDate': DateTime.now().subtract(const Duration(days: 2)),
      'isFavorite': false,
      'status': 'Saved',
    },
    {
      'fileName': 'Employee Portal',
      'type': 'Solution',
      'lastOpened': DateTime.now().subtract(const Duration(days: 3)),
      'publishedDate': DateTime.now().subtract(const Duration(days: 3)),
      'isFavorite': true,
      'status': 'Saved',
    },
    {
      'fileName': 'Financial Dashboard',
      'type': 'Solution',
      'lastOpened': DateTime.now().subtract(const Duration(days: 4)),
      'publishedDate': DateTime.now().subtract(const Duration(days: 4)),
      'isFavorite': true,
      'status': 'Saved',
    },
  ];

  // Sample published data
  final List<Map<String, dynamic>> _publishedData = [
    {
      'fileName': 'Sales Analytics Platform',
      'type': 'Solution',
      'publishedDate': DateTime.now().subtract(const Duration(days: 5)),
      'isFavorite': true,
      'status': 'Run Time',
    },
    {
      'fileName': 'Project Management Tool',
      'type': 'Solution',
      'publishedDate': DateTime.now().subtract(const Duration(days: 7)),
      'isFavorite': false,
      'status': 'Run Time',
    },
  ];

  // Filtered data getter
  List<Map<String, dynamic>> get _filteredData {
    return _filteredSampleData.isNotEmpty || _searchController.text.isNotEmpty
        ? _filteredSampleData
        : _sampleData;
  }

  @override
  void initState() {
    super.initState();
    // Initialize filtered data with original data
    _filteredSampleData = [];
    _filteredPublishedData = [];
    // Add listener to search controller
    _searchController.addListener(_onSearchChanged);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xffF7F9FB),
      body: Row(
        children: [
          Expanded(
            child: SizedBox(),
          ),
          Expanded(
            flex: 10,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Fixed header
                CustomTabHeader(),

                // Scrollable content
                Expanded(
                  child: SingleChildScrollView(
                    child: Padding(
                      padding: const EdgeInsets.all(AppSpacing.sm),
                      child: Column(
                        children: [
                          _buildMainContent(),
                          const SizedBox(height: 20),
                          // Bottom section with constrained height
                          SizedBox(
                            height: MediaQuery.of(context).size.height * 0.88,
                            child: _buildBottomSection(),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: SizedBox(),
          ),
        ],
      ),
    );
  }

  /// Builds the main content area (middle section)
  Widget _buildMainContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Publish Details Table Layout
        Container(
          width: double.infinity,
          child: Column(
            children: [
              // Tab Bar with Ready To
              //Publish and Published
              _buildPublishTabBar(),
              // Table Content
              SizedBox(
                height: 400, // Fixed height for the table
                child: _buildPublishTable(),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Builds the tab bar with Ready To Publish and Published tabs
  Widget _buildPublishTabBar() {
    final tabs = ['Ready To Publish', 'Published'];

    // Initialize selected tab if empty
    if (_selectedPublishTab.isEmpty) {
      _selectedPublishTab = tabs[0];
    }

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          ...tabs.map((tab) => _buildPublishTabItem(tab)),
          const Spacer(),
          _showSearchBar
              ? SearchBarWidget(
                  controller: _searchController,
                  onClose: _toggleSearchBar,
                )
              : GestureDetector(
                  onTap: _toggleSearchBar,
                  child: MouseRegion(
                    cursor: SystemMouseCursors.click,
                    child: Container(
                      margin: const EdgeInsets.only(right: AppSpacing.sm),
                      height: 36,
                      child: HoverableSearchIcon(),
                    ),
                  ),
                ),
        ],
      ),
    );
  }

  Widget _buildPublishTabItem(String tab) {
    final isSelected = _selectedPublishTab == tab;

    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: () {
          setState(() {
            _selectedPublishTab = tab;
          });
        },
        child: Container(
          margin: const EdgeInsets.only(right: 10),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: isSelected ? AppColors.black : Colors.transparent,
            borderRadius: BorderRadius.circular(2),
          ),
          child: Text(
            tab,
            style: TextStyle(
              fontFamily: FontManager.fontFamilyTiemposText,
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              color: isSelected ? Colors.white : AppColors.black,
            ),
          ),
        ),
      ),
    );
  }

  /// Builds the publish table with File Name, Last Opened, Status, and Action columns
  Widget _buildPublishTable() {
    // Filter data based on selected tab
    final filteredData = _getFilteredPublishData();

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: const Color(0xffD0D0D0), width: 0.5),
      ),
      child: Column(
        children: [
          // Table Header
          _buildPublishTableHeader(),
          // Table Content
          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.zero,
              itemCount: filteredData.length,
              itemBuilder: (context, index) {
                return _buildPublishTableRow(filteredData[index], index);
              },
            ),
          ),
        ],
      ),
    );
  }

  /// Builds the table header for publish table
  Widget _buildPublishTableHeader() {
    final isPublishedTab = _selectedPublishTab == 'Published';

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
      decoration: const BoxDecoration(
        //color: Color(0xFFF8F9FA),
        border: Border(
          bottom: BorderSide(color: Color(0xFFD0D0D0), width: 0.5),
          top: BorderSide(color: Color(0xFFD0D0D0), width: 0.5),
        ),
      ),
      child: Row(
        children: [
          // File Name column
          SizedBox(
            width: 300,
            child: Text(
              'File Name',
              style: TextStyle(
                fontFamily: FontManager.fontFamilyTiemposText,
                fontSize: ResponsiveFontSizes.labelSmall(context),
                fontWeight: FontWeight.w400,
                color: Colors.black,
              ),
            ),
          ),
          // Date column - changes based on tab
          SizedBox(
            width: 120,
            child: Text(
              isPublishedTab ? 'Published Date' : 'Last Opened',
              style: TextStyle(
                fontFamily: FontManager.fontFamilyTiemposText,
                fontSize: ResponsiveFontSizes.labelSmall(context),
                fontWeight: FontWeight.w400,
                color: Colors.black,
              ),
            ),
          ),
          const Spacer(),
          // Status column
          SizedBox(
            width: 60,
            child: Text(
              'Status',
              style: TextStyle(
                fontFamily: FontManager.fontFamilyTiemposText,
                fontSize: ResponsiveFontSizes.labelSmall(context),
                fontWeight: FontWeight.w400,
                color: Colors.black,
              ),
            ),
          ),
          // Add spacing between Status and Action columns
          if (!isPublishedTab) const SizedBox(width: 50),
          // Action column - only show for Ready To Publish tab
          if (!isPublishedTab)
            SizedBox(
              width: 100,
              child: Text(
                'Action',
                style: TextStyle(
                  fontFamily: FontManager.fontFamilyTiemposText,
                  fontSize: ResponsiveFontSizes.labelSmall(context),
                  fontWeight: FontWeight.w400,
                  color: Colors.black,
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// Builds individual table rows for publish table
  Widget _buildPublishTableRow(Map<String, dynamic> item, int index) {
    final status = item['status'] ?? '';
    final isPublishedTab = _selectedPublishTab == 'Published';

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
      decoration: BoxDecoration(
        color: index % 2 == 0 ? Colors.white : const Color(0xFFF4F5F5),
        border: const Border(
          bottom: BorderSide(color: Color(0xffE8E8E8), width: 1),
        ),
      ),
      child: Row(
        children: [
          // File Name with icon
          SizedBox(
            width: 300,
            child: Row(
              children: [
                _buildTypeIcon(item['type']),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        item['fileName'] ?? '',
                        style: TextStyle(
                          fontFamily: FontManager.fontFamilyTiemposText,
                          fontSize: ResponsiveFontSizes.labelLarge(context),
                          fontWeight: FontWeight.w500,
                          color: Colors.black,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        item['type'] ?? '',
                        style: TextStyle(
                            fontFamily: FontManager.fontFamilyTiemposText,
                            fontSize: ResponsiveFontSizes.labelSmall(context),
                            fontWeight: FontWeight.w400,
                            color: const Color(0xFF797676)),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          // Date column - shows Published Date for Published tab, Last Opened for Ready To Publish
          SizedBox(
            width: 120,
            child: Text(
              isPublishedTab
                  ? _formatPublishDate(
                      item['publishedDate'] ?? item['lastOpened'])
                  : _formatPublishDate(item['lastOpened']),
              style: TextStyle(
                fontFamily: FontManager.fontFamilyTiemposText,
                fontSize: ResponsiveFontSizes.labelSmall(context),
                fontWeight: FontWeight.w400,
                color: Colors.black,
              ),
            ),
          ),
          const Spacer(),
          // Status badge
          SizedBox(
            width: 60,
            child: _buildStatusBadge(
              status,
              _getStatusColor(status),
            ),
          ),
          // Add spacing between Status and Action columns
          if (!isPublishedTab) const SizedBox(width: 50),
          // Action button - only show for Ready To Publish tab
          if (!isPublishedTab)
            SizedBox(
              width: 100,
              child: ElevatedButton(
                onPressed: () => _handlePublishAction(item),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF007AFF),
                  foregroundColor: Colors.white,
                  elevation: 0,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 0, vertical: 2),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(2),
                  ),
                  minimumSize: const Size(80, 32),
                ),
                child: Text(
                  'Publish',
                  style: TextStyle(
                    fontFamily: FontManager.fontFamilyTiemposText,
                    fontSize: ResponsiveFontSizes.labelSmall(context),
                    fontWeight: FontWeight.w400,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// Filters data based on selected publish tab
  List<Map<String, dynamic>> _getFilteredPublishData() {
    if (_selectedPublishTab == 'Published') {
      return _filteredPublishedData.isNotEmpty ||
              _searchController.text.isNotEmpty
          ? _filteredPublishedData
          : _publishedData;
    } else {
      // Ready To Publish - show Draft items
      final readyToPublishData =
          _sampleData.where((item) => item['status'] == 'Saved').toList();
      if (_filteredSampleData.isNotEmpty || _searchController.text.isNotEmpty) {
        return _filteredSampleData
            .where((item) => item['status'] == 'Saved')
            .toList();
      }
      return readyToPublishData;
    }
  }

  /// Handle search text changes
  void _onSearchChanged() {
    _filterData();
  }

  /// Filter data based on search text
  void _filterData() {
    final searchText = _searchController.text.toLowerCase();

    setState(() {
      if (searchText.isEmpty) {
        _filteredSampleData = [];
        _filteredPublishedData = [];
      } else {
        // Filter sample data (Ready To Publish)
        _filteredSampleData = _sampleData
            .where((item) =>
                (item['fileName']?.toLowerCase().contains(searchText) ??
                    false) ||
                (item['type']?.toLowerCase().contains(searchText) ?? false) ||
                (item['status']?.toLowerCase().contains(searchText) ?? false))
            .toList();

        // Filter published data
        _filteredPublishedData = _publishedData
            .where((item) =>
                (item['fileName']?.toLowerCase().contains(searchText) ??
                    false) ||
                (item['type']?.toLowerCase().contains(searchText) ?? false) ||
                (item['status']?.toLowerCase().contains(searchText) ?? false))
            .toList();
      }
    });
  }

  /// Gets the appropriate color for status badges
  Color _getStatusColor(String status) {
    switch (status) {
      case 'Saved':
        return const Color(0xFF34C759); // Green color matching the image
      case 'Run Time':
        return Colors.black;
      case 'Published':
        return const Color(0xFF007AFF);
      default:
        return const Color(0xFF007AFF);
    }
  }

  /// Handles publish action for individual items
  void _handlePublishAction(Map<String, dynamic> item) {
    // Implement publish logic here
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Publish Item'),
        content: Text('Publishing "${item['fileName']}"...'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  /// Builds status badge for table
  Widget _buildStatusBadge(String status, Color color) {
    // Determine text color based on status
    Color textColor = Colors.white;
    if (status == 'Saved') {
      textColor = Colors.white; // White text on green background
    } else if (status == 'Run Time') {
      textColor = Colors.white;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(0),
      ),
      child: Text(
        status,
        textAlign: TextAlign.center,
        style: TextStyle(
          fontFamily: FontManager.fontFamilyTiemposText,
          fontSize: ResponsiveFontSizes.labelSmall(context),
          fontWeight: FontWeight.w400,
          color: textColor,
        ),
      ),
    );
  }

  /// Builds the bottom section with publish functionality
  /// Similar to the implementation in role_creation_screen.dart
  Widget _buildBottomSection() {
    final screenWidth = MediaQuery.of(context).size.width;

    // Show loading indicator while API data is being fetched
    if (_isLoadingApiData) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading library data...'),
          ],
        ),
      );
    }

    // For tablet devices (768px), use new card-based design
    if (screenWidth >= 600 && screenWidth <= 1023) {
      return _buildTabletLayout();
    }

    // For other screen sizes, use existing publish functionality
    return Consumer<ObjectCreationProvider>(
      builder: (context, provider, child) {
        // Only show bottom section if validation is successful
        if (provider.validateRoleModel != null &&
            provider.validateRoleModel?.hasErrors == false &&
            provider.saveRoleModel != null) {
          return _buildBottomPublishBar(context, provider);
        }
        return const SizedBox.shrink();
      },
    );
  }

  /// New tablet-specific layout
  Widget _buildTabletLayout() {
    // Show loading indicator for tablet layout too
    if (_isLoadingApiData) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading library data...'),
          ],
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildTabletHeader(),
        const SizedBox(height: AppSpacing.xs),
        Expanded(
          child: _buildTabletCardList(),
        ),
      ],
    );
  }

  Widget _buildTabletHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 10),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Color(0xFFE6EAEE),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // Recent tab - now clickable to show popup
          MouseRegion(
            cursor: SystemMouseCursors.click,
            child: GestureDetector(
              onTap: _showRecentModal,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                height: 42,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(
                    color: const Color(0xFFE8E8E8),
                    width: 0.5,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(
                      Icons.access_time_outlined,
                      size: 18,
                      color: AppColors.black,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      AppLocalizations.of(context)!
                          .translate('mylibrary.buttonText.recent'),
                      style: TextStyle(
                        fontFamily: FontManager.fontFamilyTiemposText,
                        fontSize: ResponsiveFontSizes.titleLarge(context),
                        fontWeight: FontWeight.w400,
                        color: const Color(0xFF242424),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          const SizedBox(width: 4),

          // Favourite tab - now clickable to show popup
          MouseRegion(
            cursor: SystemMouseCursors.click,
            child: GestureDetector(
              onTap: _showFavouriteModal,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                height: 42,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(
                    color: const Color(0xFFE8E8E8),
                    width: 0.5,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(
                      Icons.star_border,
                      size: 18,
                      color: AppColors.black,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      AppLocalizations.of(context)!
                          .translate('mylibrary.buttonText.favourite'),
                      style: TextStyle(
                          fontFamily: FontManager.fontFamilyTiemposText,
                          fontSize: ResponsiveFontSizes.titleLarge(context),
                          fontWeight: FontWeight.w400,
                          color: const Color(0xFF242424)),
                    ),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(width: 4),

          // Chat tab - now clickable to show popup from bottom
          MouseRegion(
            cursor: SystemMouseCursors.click,
            child: GestureDetector(
              onTap: _showChatModal,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                height: 42,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(
                    color: const Color(0xFFE8E8E8),
                    width: 0.5,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(
                      Icons.chat_bubble_outline,
                      size: 18,
                      color: AppColors.black,
                    ),
                    const SizedBox(width: 8),
                    const Text(
                      'Chat',
                      style: TextStyle(
                          fontFamily: FontManager.fontFamilyTiemposText,
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          color: Color(0xFF242424)),
                    ),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(width: 4),

          // Sort arrows icon - design only
          MouseRegion(
              cursor: SystemMouseCursors.click,
              child: GestureDetector(
                onTap: _showFilterModal,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 24),
                  height: 42,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(
                      color: const Color(0xFFE8E8E8),
                      width: .5,
                    ),
                  ),
                  child: const Center(
                    child: Icon(
                      Icons.swap_vert,
                      size: 22,
                      color: Colors.black, // Equivalent to a text grey
                    ),
                  ),
                ),
              )),

          const Spacer(),

          // Search input field - functional tablet search
          Container(
            width: 200,
            height: 42,
            padding: const EdgeInsets.symmetric(
              horizontal: 8,
            ),
            decoration: BoxDecoration(
              color: const Color(
                  0xFFF5F5F5), // Light gray background like in image
              borderRadius: BorderRadius.circular(4),
              border: Border.all(
                color: const Color(0xFFE8E8E8),
                width: 0.5,
              ),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                  child: TextField(
                    controller: _searchController,
                    decoration: const InputDecoration(
                      hintText: 'Search...',
                      hintStyle: TextStyle(
                        fontFamily: FontManager.fontFamilyTiemposText,
                        fontSize: 14,
                        color: AppColors.textGreyColor,
                      ),
                      border: InputBorder.none,
                      enabledBorder: InputBorder.none,
                      focusedBorder: InputBorder.none,
                      contentPadding: EdgeInsets.symmetric(vertical: 0),
                      isDense: true,
                    ),
                    style: const TextStyle(
                      fontFamily: FontManager.fontFamilyTiemposText,
                      fontSize: 14,
                      color: AppColors.black,
                    ),
                  ),
                ),
                const Icon(
                  Icons.search,
                  size: 20,
                  color: AppColors.textGreyColor,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabletCardList() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: ListView.builder(
        padding: const EdgeInsets.all(0),
        itemCount: _filteredData.length,
        itemBuilder: (context, index) {
          return _buildTabletCard(_filteredData[index], index);
        },
      ),
    );
  }

  Widget _buildTabletCard(Map<String, dynamic> item, int index) {
    final isFavorite = item['isFavorite'] ?? false;

    return Container(
      margin: const EdgeInsets.only(bottom: 1),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(
            color: Color(0xFFE8E8E8),
            width: 0.5,
          ),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 7),
        child: Row(
          children: [
            // Type icon
            _buildTypeIcon(item['type']),
            const SizedBox(width: 12),

            // Content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    item['fileName'] ?? '',
                    style: TextStyle(
                      fontFamily: FontManager.fontFamilyTiemposText,
                      fontSize: ResponsiveFontSizes.titleLarge(context),
                      fontWeight: FontWeight.w500,
                      color: AppColors.black,
                    ),
                  ),
                  const SizedBox(height: 0),
                  Text(
                    item['type'] ?? '',
                    style: TextStyle(
                      fontFamily: FontManager.fontFamilyTiemposText,
                      fontSize: ResponsiveFontSizes.titleMedium(context),
                      fontWeight: FontWeight.w400,
                      color: AppColors.textGreyColor,
                    ),
                  ),
                ],
              ),
            ),

            // Date
            Text(
              _formatTabletDate(item['lastOpened']),
              style: TextStyle(
                fontFamily: FontManager.fontFamilyTiemposText,
                fontSize: ResponsiveFontSizes.titleMedium(context),
                fontWeight: FontWeight.w400,
                color: AppColors.textGreyColor,
              ),
            ),

            const SizedBox(width: 16),

            // Favorite star
            MouseRegion(
              cursor: SystemMouseCursors.click,
              child: GestureDetector(
                onTap: () {
                  setState(() {
                    item['isFavorite'] = !item['isFavorite'];
                  });
                },
                child: Icon(
                  isFavorite ? Icons.star : Icons.star_border,
                  size: 20,
                  color: isFavorite ? Colors.amber : AppColors.textGreyColor,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTypeIcon(String type) {
    String svgAsset;

    switch (type) {
      case 'Role':
        svgAsset = 'assets/images/my_library/create_role.svg';
        break;
      case 'Object':
        svgAsset = 'assets/images/my_library/create_object.svg';
        break;
      case 'Solution':
        svgAsset = 'assets/images/my_library/create_solution.svg';
        break;
      case 'Project':
        svgAsset = 'assets/images/my_library/create_project.svg';
        break;
      default:
        svgAsset = 'assets/icons/default.svg';
    }

    return Center(
      child: CustomImage.asset(
        svgAsset,
        width: 32,
        height: 32,
        fit: BoxFit.contain,
      ).toWidget(),
    );
  }

  String _formatTabletDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date).inDays;

    if (difference == 0) {
      return 'Today';
    } else if (difference == 1) {
      return 'Yesterday';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  // Format date for publish table (same logic but separate method for clarity)
  String _formatPublishDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date).inDays;

    if (difference == 0) {
      return 'Today';
    } else if (difference == 1) {
      return 'Yesterday';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  /// Builds the bottom publish bar with publish button
  Widget _buildBottomPublishBar(
      BuildContext context, ObjectCreationProvider provider) {
    return Container(
      width: double.infinity,
      height: 60,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(
            color: Colors.grey[300]!,
            width: 1.0,
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 10.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Left side - optional status or info text
          Expanded(
            child: Text(
              'Ready to publish',
              style: FontManager.getCustomStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.grey[600],
              ),
            ),
          ),
          // Right side - action buttons
          Row(
            children: [
              // Cancel/Back button (optional)
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                style: TextButton.styleFrom(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                ),
                child: Text(
                  'Cancel',
                  style: FontManager.getCustomStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.grey[600],
                  ),
                ),
              ),
              const SizedBox(width: 12),
              // Publish button
              ElevatedButton(
                onPressed: () => _handlePublish(context, provider),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF007AFF),
                  foregroundColor: Colors.white,
                  elevation: 0,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(6),
                  ),
                  minimumSize: const Size(100, 40),
                ),
                child: Text(
                  'Publish',
                  style: FontManager.getCustomStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Handles the publish action
  Future<void> _handlePublish(
      BuildContext context, ObjectCreationProvider provider) async {
    if (!mounted) return;

    // Store context reference before async operations
    final navigator = Navigator.of(context);
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    try {
      // Show loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      // Perform publish operation
      // This would typically call a provider method to publish the entity
      // Example: await provider.publishEntity();

      // For now, simulate the publish process
      await Future.delayed(const Duration(seconds: 2));

      // Close loading dialog
      if (mounted && navigator.canPop()) {
        navigator.pop();
      }

      // Show success message
      if (mounted) {
        scaffoldMessenger.showSnackBar(
          const SnackBar(
            content: Text('Published successfully!'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 3),
          ),
        );

        // Navigate back or to success screen
        if (navigator.canPop()) {
          navigator.pop();
        }
      }
    } catch (e) {
      // Close loading dialog if still open
      if (mounted && navigator.canPop()) {
        navigator.pop();
      }

      // Show error message
      if (mounted) {
        scaffoldMessenger.showSnackBar(
          const SnackBar(
            content: Text('Failed to publish. Please try again.'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 3),
          ),
        );
      }
    }
  }

  // Modal methods for tablet layout
  void _showRecentModal() {
    // Placeholder implementation - you can customize this
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Recent Items'),
        content: const Text('Recent items modal functionality'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showFavouriteModal() {
    // Placeholder implementation - you can customize this
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Favourite Items'),
        content: const Text('Favourite items modal functionality'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showChatModal() {
    // Placeholder implementation - you can customize this
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Chat'),
        content: const Text('Chat modal functionality'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showFilterModal() {
    // Placeholder implementation - you can customize this
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filter Options'),
        content: const Text('Filter modal functionality'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  // Toggle search bar visibility
  void _toggleSearchBar() {
    setState(() {
      _showSearchBar = !_showSearchBar;
      if (!_showSearchBar) {
        // Clear search when hiding search bar
        _searchController.clear();
        // Reset filtered data when clearing search
        _filterData();
      }
    });
  }
}

/// Search bar widget that appears when search icon is clicked
class SearchBarWidget extends StatelessWidget {
  final TextEditingController controller;
  final VoidCallback onClose;

  const SearchBarWidget({
    super.key,
    required this.controller,
    required this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 250,
      height: 36,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Row(
        children: [
          // Search input field
          Expanded(
            child: TextField(
              controller: controller,
              decoration: InputDecoration(
                hintText: 'Search',
                hintStyle: TextStyle(
                  color: Colors.grey.shade500,
                  fontSize: ResponsiveFontSizes.titleSmall(context),
                  fontFamily: FontManager.fontFamilyTiemposText,
                ),
                border: InputBorder.none,
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 8.0, vertical: 0),
                isDense: true,
                hoverColor: Colors.transparent,
              ),
              style: TextStyle(
                fontSize: ResponsiveFontSizes.titleSmall(context),
                fontFamily: FontManager.fontFamilyTiemposText,
              ),
              // Auto-focus when search bar appears
              autofocus: true,
            ),
          ),

          // Close button
          GestureDetector(
            onTap: onClose,
            child: MouseRegion(
              cursor: SystemMouseCursors.click,
              child: Padding(
                padding: const EdgeInsets.only(right: 8.0),
                child: Icon(Icons.close, size: 18, color: Colors.grey.shade600),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class HoverableSearchIcon extends StatefulWidget {
  const HoverableSearchIcon({super.key});

  @override
  HoverableSearchIconState createState() => HoverableSearchIconState();
}

class HoverableSearchIconState extends State<HoverableSearchIcon> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      cursor: SystemMouseCursors.click,
      child: Container(
        width: 36,
        height: 36,
        // decoration: BoxDecoration(
        //   color: _isHovered ? const Color(0xFFE8E8E8) : const Color(0xFFF5F5F5),
        //   borderRadius: BorderRadius.circular(2),
        //   border: Border.all(
        //     color: const Color(0xFFE8E8E8),
        //     width: 0.5,
        //   ),
        // ),
        child: const Icon(
          Icons.search,
          size: 22,
        ),
      ),
    );
  }
}
