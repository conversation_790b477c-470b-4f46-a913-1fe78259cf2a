import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nsl/l10n/app_localizations.dart';
import 'package:nsl/providers/creation_provider.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/providers/web_home_provider_static.dart';
import 'package:nsl/screens/web/static_flow/ai_object_right_panel.dart';
import 'package:nsl/screens/web/static_flow/create_progress_flow.dart';
import 'package:nsl/screens/web/static_flow/extract_go_details.dart';
import 'package:nsl/screens/web/static_flow/inner_entity_screen.dart';
import 'package:nsl/screens/web/static_flow/role_creation_screen.dart';
import 'package:nsl/screens/web/static_flow/web_left_panel_solution.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/theme/app_colors.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:nsl/utils/screen_constants.dart';
import 'package:provider/provider.dart';

import '../../../providers/go_details_provider.dart';
import '../../../providers/object_creation_provider.dart';
import '../../../providers/create_entity_provider.dart';
import '../../../providers/manual_creation_provider.dart';

// void main() {
//   runApp(const MaterialApp(home: AiObjectScreenStatic()));
// }

/// Constants for the AI Object Screen Static layout
class _LayoutConstants {
  static const double middleColumnWidthNormal =
      0.50; // 70% when right panel is shown (since left column is removed)
  static const double rightColumnWidthNormal = 0.30;
  static const double leftColumnWidthNormal = 0.20;
  // 30% when right panel is shown (since left column is removed)
  static const double middleColumnWidthExpanded =
      1.0; // 100% when right panel is hidden

  static const double gripWidth = 12.0;
  static const double gripHeight = 80.0;
  static const double gripLineWidth = 6.0;
  static const double gripLineHeight = 1.5;
  static const int gripLineCount = 3;
}

/// Colors used in the AI Object Screen Static
class _ScreenColors {
  static const Color middlePanelBackground = Color(0xFFF7F9FB);
  static const Color borderColor = AppColors.greyBorder;
  static const Color gripLineColor = Color(0xFF666666);
  static const Color hoverBackground = Color(0xFFE4EDFF);
  static const Color primaryBlue = AppColors.primaryBlue;
}

class GoComingSoonScreen extends StatefulWidget {
  const GoComingSoonScreen({super.key});

  @override
  State<GoComingSoonScreen> createState() => _GoComingSoonScreenStaticState();
}

class _GoComingSoonScreenStaticState extends State<GoComingSoonScreen> {
  bool _isRightPanelVisible = false;
  double _middleColumnWidth = _LayoutConstants.middleColumnWidthNormal;

  @override
  void initState() {
    super.initState();
    _updateColumnWidths();
    // Show left panel by default
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final creationProvider =
          Provider.of<CreationProvider>(context, listen: false);
      creationProvider.isLeftPanelVisible = true;
    });
  }

  /// Updates column widths based on panel visibility
  void _updateColumnWidths() {
    if (_isRightPanelVisible) {
      _middleColumnWidth = _LayoutConstants.middleColumnWidthNormal;
    } else {
      _middleColumnWidth = _LayoutConstants.middleColumnWidthExpanded;
    }
  }

  /// Toggles the right panel visibility
  void _toggleRightPanel() {
    setState(() {
      _isRightPanelVisible = !_isRightPanelVisible;
      _updateColumnWidths();
    });
  }

  /// Toggles the left panel visibility using CreationProvider
  void _toggleLeftPanel() {
    final creationProvider =
        Provider.of<CreationProvider>(context, listen: false);
    creationProvider.toggleLeftPanel();
  }

  /// Gets the progress flow steps for the header
  List<ProgressFlowStep> _getProgressFlowSteps() {
    return [
      const ProgressFlowStep(
        title: 'GO Details',
        state: ProgressFlowState.done,
      ),
      const ProgressFlowStep(
        title: 'LO Details',
        state: ProgressFlowState.pending,
      ),
      const ProgressFlowStep(
        title: 'Attributes',
        state: ProgressFlowState.pending,
      ),
      const ProgressFlowStep(
        title: 'Inputs',
        state: ProgressFlowState.pending,
      ),
      const ProgressFlowStep(
        title: 'Nested',
        state: ProgressFlowState.disabled,
      ),
      const ProgressFlowStep(
        title: 'Output',
        state: ProgressFlowState.disabled,
      ),
      const ProgressFlowStep(
        title: 'Pathways',
        state: ProgressFlowState.disabled,
      ),
    ];
  }

  /// Shows the delete object confirmation dialog matching the design
  void _showDeleteObjectConfirmation(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext dialogContext) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          child: Container(
            width: 549,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Center(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header with warning icon and close button
                      Container(
                        padding: const EdgeInsets.only(
                            left: 16, right: 16, bottom: 10, top: 10),
                        decoration: const BoxDecoration(
                          border: Border(
                            bottom:
                                BorderSide(color: Color(0xFFE5E7EB), width: 1),
                          ),
                        ),
                        child: Row(
                          children: [
                            // Warning icon
                            Container(
                              width: 24,
                              height: 24,
                              decoration: const BoxDecoration(
                                color: Color(0xFFFFFFFF),
                                shape: BoxShape.circle,
                              ),
                              child: const Icon(
                                Icons.warning_amber_rounded,
                                color: Color(0xFFFF2019),
                                size: 16,
                              ),
                            ),
                            const SizedBox(width: 12),
                            // Title
                            const Expanded(
                              child: Text(
                                'Attention',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.black,
                                ),
                              ),
                            ),
                            // Close button
                            IconButton(
                              onPressed: () =>
                                  Navigator.of(dialogContext).pop(),
                              icon: const Icon(Icons.close),
                              iconSize: 20,
                              color: Colors.grey[600],
                              padding: EdgeInsets.zero,
                              constraints: const BoxConstraints(),
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 20),

                      // Main message
                      Padding(
                        padding: const EdgeInsets.only(left: 26, right: 26),
                        child: Center(
                          child: Text(
                            '"If you go back now, your document will not be saved. Do you want to continue editing and save your work instead?"',
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.titleLarge(context),
                              fontWeight: FontWeight.w500,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                              height: 1.4,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                      const SizedBox(height: 26),

                      // Options text
                      Center(
                        child: Text(
                          'Options:',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.titleLarge(context),
                            fontWeight: FontWeight.w500,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      const SizedBox(height: 8),

                      // Option 1
                      Center(
                        child: Text(
                          'Continue Editing & Save',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.titleLarge(context),
                            fontWeight: FontWeight.w500,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      const SizedBox(height: 4),

                      // Option 2
                      Center(
                        child: Text(
                          'Back Leave Without Saving',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.titleLarge(context),
                            fontWeight: FontWeight.w500,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      const SizedBox(height: 24),
                      // Action buttons
                      Container(
                        padding: const EdgeInsets.only(
                            left: 16, right: 16, bottom: 10, top: 10),
                        decoration: const BoxDecoration(
                          border: Border(
                            top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            // Back button
                            TextButton(
                              onPressed: () {
                                Navigator.of(dialogContext)
                                    .pop(); // Close the dialog first
                                _clearAllDataAndNavigateBack();
                                Provider.of<WebHomeProvider>(context,
                                            listen: false)
                                        .currentScreenIndex =
                                    ScreenConstants.createObjectScreenStatic;
                              },
                              style: TextButton.styleFrom(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 16, vertical: 8),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(6),
                                  side: BorderSide(color: Colors.grey[300]!),
                                ),
                              ),
                              child: const Text(
                                'Back',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.black87,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                            const SizedBox(width: 12),

                            // Continue button
                            ElevatedButton(
                              onPressed: () {
                                Navigator.of(dialogContext)
                                    .pop(); // Close the dialog
                                // Continue editing - stay on current screen
                                // Provider.of<WebHomeProvider>(context,
                                //           listen: false)
                                //       .currentScreenIndex =
                                //   ScreenConstants.createObjectScreenStatic;
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor:
                                    const Color(0xFF007AFF), // Blue color
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 16, vertical: 8),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                elevation: 0,
                              ),
                              child: const Text(
                                'Continue',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// Shows the cancel confirmation dialog
  void _showCancelConfirmation(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext dialogContext) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          child: Container(
            width: 549,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Center(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header with warning icon and close button
                      Container(
                        padding: const EdgeInsets.only(
                            left: 16, right: 16, bottom: 10, top: 10),
                        decoration: const BoxDecoration(
                          border: Border(
                            bottom:
                                BorderSide(color: Color(0xFFE5E7EB), width: 1),
                          ),
                        ),
                        child: Row(
                          children: [
                            // Warning icon
                            Container(
                              width: 24,
                              height: 24,
                              decoration: const BoxDecoration(
                                color: Color(0xFFFFFFFF),
                                shape: BoxShape.circle,
                              ),
                              child: const Icon(
                                Icons.warning_amber_rounded,
                                color: Color(0xFFFF2019),
                                size: 16,
                              ),
                            ),
                            const SizedBox(width: 12),
                            // Title
                            const Expanded(
                              child: Text(
                                'Cancel Confirmation',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.black,
                                ),
                              ),
                            ),
                            // Close button
                            IconButton(
                              onPressed: () =>
                                  Navigator.of(dialogContext).pop(),
                              icon: const Icon(Icons.close),
                              iconSize: 20,
                              color: Colors.grey[600],
                              padding: EdgeInsets.zero,
                              constraints: const BoxConstraints(),
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 20),

                      // Main message
                      Padding(
                        padding: const EdgeInsets.only(left: 26, right: 26),
                        child: Center(
                          child: Text(
                            'Are you sure you want to cancel? All entered data will be lost.',
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.titleLarge(context),
                              fontWeight: FontWeight.w500,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                              height: 1.4,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                      const SizedBox(height: 26),

                      // Action buttons
                      Container(
                        padding: const EdgeInsets.only(
                            left: 16, right: 16, bottom: 10, top: 10),
                        decoration: const BoxDecoration(
                          border: Border(
                            top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            // No button
                            TextButton(
                              onPressed: () {
                                Navigator.of(dialogContext)
                                    .pop(); // Close the dialog
                              },
                              style: TextButton.styleFrom(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 16, vertical: 8),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(6),
                                  side: BorderSide(color: Colors.grey[300]!),
                                ),
                              ),
                              child: const Text(
                                'No',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.black87,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                            const SizedBox(width: 12),

                            // Yes button
                            ElevatedButton(
                              onPressed: () {
                                Navigator.of(dialogContext)
                                    .pop(); // Close the dialog
                                _clearAllDataAndReset();
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor:
                                    const Color(0xFF007AFF), // Blue color
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 16, vertical: 8),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                elevation: 0,
                              ),
                              child: const Text(
                                'Yes',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// Clears all data and resets the GO page to fresh state
  void _clearAllDataAndReset() {
    // Get the GoDetailsProvider and clear all data
    final goDetailsProvider =
        Provider.of<GoDetailsProvider>(context, listen: false);

    // Clear all data including local objectives
    goDetailsProvider.clearAllData();

    // Clear local objectives data in Go_model specifically
    if (goDetailsProvider.currentGoModel != null) {
      goDetailsProvider.currentGoModel!.localObjectivesList?.clear();
      goDetailsProvider.currentGoModel!.globalObjectives?.name = '';
      goDetailsProvider.currentGoModel!.globalObjectives?.description = '';
      goDetailsProvider.currentGoModel!.globalObjectives?.isValidated = false;
      goDetailsProvider.currentGoModel!.globalObjectives?.isPublished = false;
    }

    // Reset the creation provider to refresh the page
    final creationProvider =
        Provider.of<CreationProvider>(context, listen: false);
    creationProvider.currentMiddleScreen = 0; // Reset to initial screen

    // Force refresh by rebuilding the widget
    setState(() {});

    // Show success message
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('All data cleared successfully. Page refreshed.'),
        backgroundColor: Colors.green,
        duration: Duration(seconds: 2),
      ),
    );
  }

  /// Clears all data related to GO, role, and entity and navigates back
  void _clearAllDataAndNavigateBack() {
    // Clear GoDetailsProvider data
    final goDetailsProvider =
        Provider.of<GoDetailsProvider>(context, listen: false);
    goDetailsProvider.clearAllData();

    // Clear ObjectCreationProvider data (used for entities)
    final objectCreationProvider =
        Provider.of<ObjectCreationProvider>(context, listen: false);
    objectCreationProvider.clearData();

    // Clear CreationProvider data (used for roles and navigation)
    final creationProvider =
        Provider.of<CreationProvider>(context, listen: false);
    creationProvider.clearRoleData();
    creationProvider.currentMiddleScreen = 0; // Reset to initial screen

    // Clear CreateEntityProvider data (used for entity creation)
    final createEntityProvider =
        Provider.of<CreateEntityProvider>(context, listen: false);
    createEntityProvider.resetObject();

    // Clear ManualCreationProvider data (used for various creation workflows)
    final manualCreationProvider =
        Provider.of<ManualCreationProvider>(context, listen: false);
    manualCreationProvider.clearValidationResults();

    // Navigate back to the previous screen
    Provider.of<WebHomeProvider>(context, listen: false).currentScreenIndex =
        ScreenConstants.createObjectScreenStatic;

    // Show success message
    // ScaffoldMessenger.of(context).showSnackBar(
    //   const SnackBar(
    //     content: Text('All data cleared and navigating back.'),
    //     backgroundColor: Colors.green,
    //     duration: Duration(seconds: 2),
    //   ),
    // );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          // _buildSubHeader(),
          _buildMainContent(),
        ],
      ),
    );
  }

  /// Builds the header section with navigation and toggle button
  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: 13,
        vertical: 5,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(
            color: Color(0xFFD0D0D0),
            width: .5,
          ),
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Left side - Back button with text and collapse icon
          Row(
            children: [
              // Back button with icon and text
              InkWell(
                onTap: () {
                  _showDeleteObjectConfirmation(context);

                },
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SvgPicture.asset(
                      'assets/images/back-flow.svg',
                      width: 10,
                      height: 10,
                      colorFilter: ColorFilter.mode(
                        Colors.black,
                        BlendMode.srcIn,
                      ),
                    ),
                    SizedBox(width: 2),
                    Text(
                      'Back',
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: ResponsiveFontSizes.labelSmall(context),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(width: AppSpacing.xs),
              // Collapse icon with rotation based on panel state
              Consumer<CreationProvider>(
                builder: (context, creationProvider, child) {
                  return InkWell(
                    onTap: _toggleLeftPanel,
                    child: Transform.rotate(
                      angle: creationProvider.isLeftPanelVisible
                          ? 3.14159
                          : 0, // 180 degrees when panel is visible
                       child: Icon(
                  Icons.login,
                  size: AppSpacing.size14,
                  color: 
                  // _isHovered
                  //     ? _ScreenColors.primaryBlue
                      // :
                       Colors.grey.shade600,
                ),
                       // SvgPicture.asset(
                      //   'assets/images/colaps-flow.svg',
                      //   // width: 16,
                      //   // height: 16,
                      //   colorFilter: ColorFilter.mode(
                      //     Colors.black,
                      //     BlendMode.srcIn,
                      //   ),
                      // ),
                    ),
                  );
                },
              ),
            ],
          ),

          // Center - Progress Flow Bar
          Expanded(
            child: Center(
              child: CreateProgressFlowBar(
                steps: _getProgressFlowSteps(),
                height: 40,
                bulletSize: 12,
                showTitles: true,
                titleStyle: TextStyle(
                  fontSize: ResponsiveFontSizes.labelSmall(context),
                  fontWeight: FontWeight.w500,
                ),
                doneColor: const Color(0xFF4CAF50),
                pendingColor: const Color(0xFFFF9800),
                disabledColor: const Color(0xFFBDBDBD),
                lineColor: const Color(0xFFE0E0E0),
                textSpacing: 4,
              ),
            ),
          ),

          // Right side - Three icons plus the existing expand button
          Row(
            children: [
              // First icon
              InkWell(
                onTap: () {
                  // Add functionality for first icon
                },
                child: SvgPicture.asset(
                  'assets/images/flow-icon-1.svg',
                  // colorFilter: ColorFilter.mode(
                  //   Colors.black,
                  //   BlendMode.srcIn,
                  // ),
                ),
              ),
              SizedBox(width: 8),
              // Second icon
              InkWell(
                onTap: () {
                  // Add functionality for second icon
                },
                child: SvgPicture.asset(
                  'assets/images/flow-icon-2.svg',
                  // colorFilter: ColorFilter.mode(
                  //   Colors.black,
                  //   BlendMode.srcIn,
                  // ),
                ),
              ),
              SizedBox(width: 8),
              // Third icon
              InkWell(
                onTap: () {
                  // Add functionality for third icon
                },
                child: SvgPicture.asset(
                  'assets/images/flow-icon-3.svg',
                  // colorFilter: ColorFilter.mode(
                  //   Colors.black,
                  //   BlendMode.srcIn,
                  // ),
                ),
              ),
              SizedBox(width: 8),
              // Keep the existing expand button (last right icon unchanged)
              _HoverExpandButton(
                isExpanded: _isRightPanelVisible,
                onTap: _toggleRightPanel,
                tooltipMessage: _isRightPanelVisible
                    ? 'Expand middle panel'
                    : 'Show right panel',
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Builds the subheader section with tab-like elements
  Widget _buildSubHeader() {
    return Container(
      height: 20,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(
            color: Color(0xFFD0D0D0),
            width: .5,
          ),
        ),
      ),
      child: Row(
        children: [
          // Tab for Customer object
          _buildTab('Customer', false, () {
            // Handle tab close
          }),
          // Tab for Object_2 (dark/black tab)
          _buildTab('Object_2', true, () {
            // Handle tab close
          }),
          // Spacer to fill remaining space
          Expanded(child: Container()),
        ],
      ),
    );
  }

  /// Builds individual tab with text and close icon
  Widget _buildTab(String title, bool isDark, VoidCallback onClose) {
    return Container(
      height: 20,
      constraints: BoxConstraints(
        minWidth: 80,
        maxWidth: 130,
      ),
      decoration: BoxDecoration(
        color: isDark ? Colors.black : Colors.white,
        border: Border(
          right: BorderSide(
            color: Color(0xFFE5E7EB),
            width: 1,
          ),
        ),
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 12),
        child: Row(
          children: [
            // Tab title
            Expanded(
              child: Text(
                title,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.labelSmall(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: isDark ? Colors.white : Colors.black,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            SizedBox(width: 8),
            // Close icon
            InkWell(
              onTap: onClose,
              borderRadius: BorderRadius.circular(8),
              child: Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  shape: BoxShape.rectangle,
                  borderRadius: BorderRadius.circular(2),
                  border: Border.all(
                    color: isDark ? Colors.white : Colors.grey[400]!,
                    width: 1,
                  ),
                ),
                child: Icon(
                  Icons.close,
                  size: 10,
                  color: isDark ? Colors.white : Colors.grey[600],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds the main content with 2-column layout
  Widget _buildMainContent() {
    Widget middleWidget = Container();
    return Expanded(
      child: LayoutBuilder(
        builder: (context, constraints) {
          return Consumer2<WebHomeProviderStatic, CreationProvider>(
            builder: (context, value, provider, child) {
              if (provider.currentMiddleScreen == 0) {
                // Use a static key to preserve widget state
                middleWidget = RoleCreationScreen(
                    key: ValueKey('role_creation_screen'));
              } else if (provider.currentMiddleScreen == 2 ||
                  provider.isGoMyLibraryClicked) {
                middleWidget = ExtractGoDetailsMiddleStatic();
              } else if (provider.currentMiddleScreen == 1) {
                middleWidget = InnerEntityScreen();
              }
              return Column(
                children: [
                  Expanded(
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Left panel with conditional visibility
                        if (provider.isLeftPanelVisible)
                          SizedBox(
                            width: constraints.maxWidth *
                                _LayoutConstants.leftColumnWidthNormal,
                            child: WebLeftPanelSolution(),
                          ),
                        // Middle column takes up the remaining space
                        Expanded(
                          child: _buildMiddleColumn(constraints, middleWidget),
                        ),

                        // Only render right column when panel is visible
                        if (_isRightPanelVisible)
                          _buildRightColumn(constraints),
                      ],
                    ),
                  ),
                  _buildFooter(context, constraints,
                      Provider.of<GoDetailsProvider>(context, listen: false))
                ],
              );
              // return Row(
              //   crossAxisAlignment: CrossAxisAlignment.start,
              //   children: [

              //     // Middle column takes up the remaining space
              //     _buildMiddleColumn(constraints),
              //     // Only render right column when panel is visible
              //     if (_isRightPanelVisible) _buildRightColumn(constraints),
              //   ],
              // );
            },
          );
        },
      ),
    );
  }

  Widget _buildFooter(BuildContext context, BoxConstraints constraints,
      GoDetailsProvider goDetailsProvider) {
    return Consumer<GoDetailsProvider>(
      builder: (context, provider, child) {
        if (!provider.showFooter) {
          return const SizedBox.shrink(); // Hide footer if flag is false
        }
        return Container(
            height: 40,
            padding: _isRightPanelVisible
                ? EdgeInsets.only(
                    right: constraints.maxWidth *
                        _LayoutConstants.rightColumnWidthNormal,
                  )
                : EdgeInsets.only(right: 10),

            //color:Colors.red,
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.2),
                  spreadRadius: 0,
                  blurRadius: 10,
                  offset: Offset(0, -5), // Negative Y offset for top shadow
                ),
              ],
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                // Show LO-specific buttons when LO details are being shown
                if (goDetailsProvider.showLocalObjectiveDetails) ...[
                  // LO Cancel button
                  InkWell(
                    onTap: () {
                      goDetailsProvider.cancelLocalObjectiveDetails();
                    },
                    child: Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: AppSpacing.sm,
                        vertical: AppSpacing.size6,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(AppSpacing.xxs),
                        border: Border.all(color: Colors.grey),
                      ),
                      child: Text(
                        'LO Cancel',
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.labelSmall(context),
                          fontWeight: FontManager.regular,
                          color: Colors.black,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          height: 1.2,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: AppSpacing.size10),
                  goDetailsProvider.shouldShowLoValidateButton(
                          goDetailsProvider.selectedLocalObjectiveIndex ?? 0)
                      ?
                      // LO Validate button
                      InkWell(
                          onTap: () async {
                            Map<String, dynamic> validationResult =
                                await goDetailsProvider
                                    .validateLocalObjective();

                            final List clientErrors =
                                validationResult['clientErrors'] ?? [];
                            final String? apiError =
                                validationResult['apiError'];

                            if (clientErrors.isNotEmpty) {
                              // Show client-side validation errors in SnackBar
                              final errorMessage = clientErrors.join('\n');
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text(errorMessage),
                                  backgroundColor: Colors.red,
                                  duration: Duration(seconds: 5),
                                  action: SnackBarAction(
                                    label: 'Dismiss',
                                    textColor: Colors.white,
                                    onPressed: () {
                                      ScaffoldMessenger.of(context)
                                          .hideCurrentSnackBar();
                                    },
                                  ),
                                ),
                              );
                            } else if (apiError != null) {
                              // Show API validation errors in AlertDialog (like GO validations)
                              await showDialog(
                                context: context,
                                builder: (context) {
                                  return validationErrorDialog(
                                      context, "LO Validation", apiError);
                                },
                              );
                            } else {
                              // Success case
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text(
                                      'Local Objective LO-${(goDetailsProvider.selectedLocalObjectiveIndex ?? 0) + 1} validated successfully.'),
                                  backgroundColor: Colors.green,
                                  duration: Duration(seconds: 3),
                                ),
                              );
                              // Hide LO details after successful validation
                              // goDetailsProvider
                              //     .hideLocalObjectiveDetails();
                            }
                          },
                          child: Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: AppSpacing.sm,
                              vertical: AppSpacing.size6,
                            ),
                            decoration: BoxDecoration(
                              color: const Color(0xFF0058FF),
                              borderRadius:
                                  BorderRadius.circular(AppSpacing.xxs),
                            ),
                            child: Text(
                              'LO Validate',
                              style: FontManager.getCustomStyle(
                                fontSize:
                                    ResponsiveFontSizes.labelSmall(context),
                                fontWeight: FontManager.regular,
                                color: Colors.white,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                height: 1.2,
                              ),
                            ),
                          ),
                        )
                      : goDetailsProvider.shouldShowLoPublishButton(
                              goDetailsProvider.selectedLocalObjectiveIndex ??
                                  0)
                          ? InkWell(
                              onTap: () async {
                                if ((goDetailsProvider.currentGoModel
                                            ?.globalObjectives?.isValidated ??
                                        false) &&
                                    !(goDetailsProvider.currentGoModel
                                            ?.globalObjectives?.isPublished ??
                                        false)) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text(
                                          'Without GO Publish, LO cannot be published. Please publish GO first.'),
                                      backgroundColor: Colors.red,
                                      duration: Duration(seconds: 3),
                                    ),
                                  );
                                  return;
                                } else {
                                  String? errorMessage = await goDetailsProvider
                                      .publishLo(goDetailsProvider
                                              .selectedLocalObjectiveIndex ??
                                          0);
                                  if (errorMessage != null) {
                                    await showDialog(
                                      context: context,
                                      builder: (context) {
                                        return validationErrorDialog(context,
                                            "LO Publish", errorMessage);
                                      },
                                    );
                                  } else {
                                    // Success case
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content: Text(
                                            'Local Objective LO-${(goDetailsProvider.selectedLocalObjectiveIndex ?? 0) + 1} published successfully.'),
                                        backgroundColor: Colors.green,
                                        duration: Duration(seconds: 3),
                                      ),
                                    );
                                  }
                                }
                              },
                              child: Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal: AppSpacing.sm,
                                  vertical: AppSpacing.size6,
                                ),
                                decoration: BoxDecoration(
                                  color: const Color(0xFF0058FF),
                                  borderRadius:
                                      BorderRadius.circular(AppSpacing.xxs),
                                ),
                                child: Text(
                                  'LO Publish',
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.labelSmall(context),
                                    fontWeight: FontManager.regular,
                                    color: Colors.white,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    height: 1.2,
                                  ),
                                ),
                              ),
                            )
                          : goDetailsProvider.shouldShowLoPublishedStatus(
                                  goDetailsProvider
                                          .selectedLocalObjectiveIndex ??
                                      0)
                              ? Container(
                                  padding: EdgeInsets.symmetric(
                                    horizontal: AppSpacing.sm,
                                    vertical: AppSpacing.size6,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Colors.green,
                                    borderRadius:
                                        BorderRadius.circular(AppSpacing.xxs),
                                  ),
                                  child: Text(
                                    'LO Published',
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.labelSmall(
                                          context),
                                      fontWeight: FontManager.regular,
                                      color: Colors.white,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      height: 1.2,
                                    ),
                                  ),
                                )
                              : SizedBox(),
                ] else ...[
                  // Show GO-specific buttons when LO details are not being shown
                  InkWell(
                    onTap: () {
                      _showCancelConfirmation(context);
                    },
                    child: Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: AppSpacing.sm,
                        vertical: AppSpacing.size6,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(AppSpacing.xxs),
                        border: Border.all(color: Colors.grey),
                      ),
                      child: Text(
                        context.tr('chat.cancel'),
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.labelSmall(context),
                          fontWeight: FontManager.regular,
                          color: Colors.black,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          height: 1.2,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: AppSpacing.size10),
                  // Show Validate button if GO is not validated yet
                  (!(goDetailsProvider.currentGoModel?.globalObjectives
                                  ?.isValidated ??
                              false)) &&
                          goDetailsProvider.isGoValidateVisible
                      ? InkWell(
                          onTap: () async {
                            bool isValidate =
                                await goDetailsProvider.validateGO();
                            if (isValidate) {
                              await showDialog(
                                context: context,
                                builder: (context) {
                                  return validationErrorDialog(
                                      context, "GO creation", "");
                                },
                              );
                            } else {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text(
                                      'Go validated and saved successfully.'),
                                  backgroundColor: Colors.green,
                                  duration: Duration(seconds: 3),
                                ),
                              );
                            }
                          },
                          child: Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: AppSpacing.sm,
                              vertical: AppSpacing.size6,
                            ),
                            decoration: BoxDecoration(
                              color: const Color(0xFF0058FF),
                              borderRadius:
                                  BorderRadius.circular(AppSpacing.xxs),
                            ),
                            child: Text(
                              context.tr('build.validate'),
                              style: FontManager.getCustomStyle(
                                fontSize:
                                    ResponsiveFontSizes.labelSmall(context),
                                fontWeight: FontManager.regular,
                                color: Colors.white,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                height: 1.2,
                              ),
                            ),
                          ),
                        )
                      // Show Publish button if GO is validated but not published yet
                      : (goDetailsProvider.currentGoModel?.globalObjectives
                                      ?.isValidated ??
                                  false) &&
                              !(goDetailsProvider.currentGoModel
                                      ?.globalObjectives?.isPublished ??
                                  false)
                          ? InkWell(
                              onTap: () async {
                                bool hasError =
                                    await goDetailsProvider.publishGo();
                                if (hasError) {
                                  await showDialog(
                                    context: context,
                                    builder: (context) {
                                      return validationErrorDialog(
                                          context, "GO creation", "");
                                    },
                                  );
                                } else {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content:
                                          Text('Go published successfully.'),
                                      backgroundColor: Colors.green,
                                      duration: Duration(seconds: 3),
                                    ),
                                  );
                                }
                              },
                              child: Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal: AppSpacing.sm,
                                  vertical: AppSpacing.size6,
                                ),
                                decoration: BoxDecoration(
                                  color: const Color(0xFF0058FF),
                                  borderRadius:
                                      BorderRadius.circular(AppSpacing.xxs),
                                ),
                                child: Text(
                                  context.tr('build.publish'),
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.labelSmall(context),
                                    fontWeight: FontManager.regular,
                                    color: Colors.white,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    height: 1.2,
                                  ),
                                ),
                              ),
                            )
                          : SizedBox(),
                ],
              ],
            ));
      },
    );
  }

  // _buildFooter(BuildContext context, BoxConstraints constraints) {
  //   final provider = Provider.of<CreationProvider>(context, listen: false);
  //   return (provider.currentMiddleScreen == 2 || provider.isGoMyLibraryClicked)
  //       ? Consumer<GoDetailsProvider>(
  //           builder: (context, goDetailsProvider, child) {
  //             return Container();
  //           },
  //         )
  //       : Container();
  // }

  Widget validationErrorDialog(
      BuildContext context, String title, String errorString) {
    final provider = Provider.of<GoDetailsProvider>(context, listen: false);

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Container(
        width: math.min(MediaQuery.of(context).size.width * 0.8, 534),
        height: math.min(MediaQuery.of(context).size.height * 0.8, 600),
        child: Column(
          children: [
            // Header with bottom border
            Container(
              padding: const EdgeInsets.only(
                  bottom: 14, top: 14, left: 24, right: 24),
              decoration: const BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '$title Validation Error',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.bodyLarge(context),
                      fontWeight: FontWeight.w600,
                      fontFamily: FontManager.fontFamilyInter,
                      color: Colors.black,
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                    iconSize: 24,
                    color: Colors.grey[600],
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                ],
              ),
            ),
            Expanded(
                child: Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: 24, vertical: 8.0),
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Purpose',
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.bodyLarge(context),
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(
                      height: AppSpacing.xs,
                    ),
                    Text(
                      "We encountered one or more issues. Please review the following:",
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.titleSmall(context),
                        fontWeight: FontWeight.w400,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(
                      height: AppSpacing.xs,
                    ),
                    Expanded(
                      child: Container(
                        width: double.infinity,
                        decoration: BoxDecoration(
                          color: Color(0xffF8FAFC),
                          border: Border.all(color: Color(0xffE5E7EA)),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        padding: EdgeInsets.all(AppSpacing.sm),
                        child: SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            spacing: AppSpacing.xs,
                            children: [
                              if (errorString.isNotEmpty)
                                Text(
                                  errorString,
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.titleSmall(context),
                                    fontWeight: FontWeight.w400,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.black,
                                  ),
                                ),
                              if (provider.parseValidateGo?.validationErrors !=
                                  null)
                                for (var element in provider
                                    .parseValidateGo?.validationErrors!)
                                  Text(
                                    "${element["message"] ?? ''}\n",
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ]),
            )),
            // Footer with top border
            Container(
              padding: const EdgeInsets.only(
                  bottom: 14, top: 14, left: 24, right: 24),
              decoration: const BoxDecoration(
                border: Border(
                  top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop(false);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF0058FF),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 24, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      elevation: 0,
                    ),
                    child: Text(
                      'Proceed',
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.bodyMedium(context),
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds the middle column
  Widget _buildMiddleColumn(BoxConstraints constraints, Widget middleWidget) {
    return SizedBox(
      width: constraints.maxWidth * _middleColumnWidth,
      child: _buildMiddlePanelContent(middleWidget),
    );
  }

  /// Builds the main middle panel content
  Widget _buildMiddlePanelContent(Widget middleWidget) {
    return Container(
      decoration: const BoxDecoration(
        color: _ScreenColors.middlePanelBackground,
        // border: Border(
        //   left: BorderSide(color: _ScreenColors.borderColor, width: 1),
        //   right: BorderSide(color: _ScreenColors.borderColor, width: 1),
        // ),
      ),
      child: Padding(
          padding: EdgeInsets.only( right: 0), child: middleWidget),
    );
  }

  /// Builds the right column (AI Object Right Panel)
  Widget _buildRightColumn(BoxConstraints constraints) {
    return SizedBox(
      width: constraints.maxWidth * _LayoutConstants.rightColumnWidthNormal,
      child: Padding(
        padding: EdgeInsets.only(left: 8, right: 10),
        child: const AiObjectRightPanel(),
      ),
    );
  }
}

/// Expand/collapse button with hover effect for panel toggle
class _HoverExpandButton extends StatefulWidget {
  final bool isExpanded;
  final VoidCallback onTap;
  final String tooltipMessage;

  const _HoverExpandButton({
    required this.isExpanded,
    required this.onTap,
    this.tooltipMessage = '',
  });

  @override
  State<_HoverExpandButton> createState() => _HoverExpandButtonState();
}

class _HoverExpandButtonState extends State<_HoverExpandButton> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      cursor: SystemMouseCursors.click,
      child: Tooltip(
        message: widget.tooltipMessage,
        child: GestureDetector(
          onTap: widget.onTap,
          child: Container(
            width: AppSpacing.sm,
            height: AppSpacing.sm,
            decoration: BoxDecoration(
              color: _isHovered
                  ? _ScreenColors.hoverBackground
                  : Colors.transparent,
              borderRadius: BorderRadius.circular(AppSpacing.xxs),
            ),
            child: Center(
              child: Transform(
                alignment: Alignment.center,
                transform: Matrix4.identity()
                  ..scale(widget.isExpanded ? 1.0 : -1.0, 1.0, 1.0),
                child: Icon(
                  Icons.login,
                  size: AppSpacing.size14,
                  color: _isHovered
                      ? _ScreenColors.primaryBlue
                      : Colors.grey.shade600,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
